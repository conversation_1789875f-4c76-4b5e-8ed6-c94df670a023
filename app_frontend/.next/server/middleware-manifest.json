{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c3420690._.js", "server/edge/chunks/[root-of-the-server]__2b98656d._.js", "server/edge/chunks/edge-wrapper_10e7b869.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public|auth).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public|auth).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7BAxWa5p+b8q2J3NhX0DnvcMhngNF7xSgxZk655tnQA=", "__NEXT_PREVIEW_MODE_ID": "0d682f6a59103f5150ab7ea0e5748d1e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7687134a5d8d0692353219717d8ea1e7bff83f5c86425297c0477ec6f03ab1d7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5631eb26462448248b9fd370506f44653be026882ccba3b42aa1fd15bc7cec59"}}}, "sortedMiddleware": ["/"], "functions": {}}