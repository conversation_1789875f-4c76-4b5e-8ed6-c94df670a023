import NextAuth, { DefaultSession } from "next-auth"

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      id: string;
      // Add other properties from DefaultSession.user if needed
      name?: string | null;
      email?: string | null;
      image?: string | null;
    } & DefaultSession["user"];
    error?: string;
  }

  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   */
  interface User {
    id: string;
    // Add other properties from DefaultUser if needed
  }
}

// If you are using JWTs for sessions, you may need to add `id` to the `JWT` type as well
import { JWT } from "next-auth/jwt"

declare module "next-auth/jwt" {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    id: string;
    // Add other properties from DefaultJWT if needed
  }
}