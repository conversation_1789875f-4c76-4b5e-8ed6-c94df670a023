import { getServerSession } from "next-auth/next"
import type { Session } from "next-auth";
import type { AuthError, AuthResponse } from "../../types/auth";
import { AuthErrorType } from "../../types/auth";

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"

/**
 * Get the current session from NextAuth
 */
export async function getCurrentSession(): Promise<Session | null> {
  try {
    const session = await getServerSession()
    return session
  } catch (error) {
    console.error("Error getting session:", error)
    return null
  }
}

/**
 * Get auth headers for API requests
 */
export function getAuthHeaders(accessToken?: string): HeadersInit {
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  }

  if (accessToken) {
    headers["Authorization"] = `Bearer ${accessToken}`
  }

  return headers
}


/**
 * Standardize authentication errors
 */
export function standardizeAuthError(error: any): AuthError {
  if (error?.response?.status === 401) {
    return {
      type: AuthErrorType.CredentialsSignIn,
      message: "Invalid email or password",
      code: "INVALID_CREDENTIALS",
    }
  }

  if (error?.response?.status === 404) {
    return {
      type: AuthErrorType.CredentialsSignIn,
      message: "User not found",
      code: "USER_NOT_FOUND",
    }
  }

  if (error?.response?.status === 429) {
    return {
      type: AuthErrorType.Default,
      message: "Too many attempts. Please try again later",
      code: "RATE_LIMIT_EXCEEDED",
    }
  }

  if (error?.message?.includes("OAuth")) {
    return {
      type: AuthErrorType.OAuthSignIn,
      message: error.message,
      code: "OAUTH_ERROR",
    }
  }

  return {
    type: AuthErrorType.Default,
    message: error?.message || "An unexpected error occurred",
    code: "UNKNOWN_ERROR",
  }
}

/**
 * Validate session and check if it's expired
 */
export function isSessionValid(session: Session | null): boolean {
  if (!session) return false
  
  // Check if session has required fields
  if (!session.user?.id || !session.user?.email) return false
  
  // If there's an error in the session, it's invalid
  if (session.error) return false
  
  return true
}

/**
 * Get user-friendly error messages
 */
export function getAuthErrorMessage(errorType: AuthErrorType): string {
  switch (errorType) {
    case AuthErrorType.CredentialsSignIn:
      return "Invalid email or password. Please check your credentials and try again."
    case AuthErrorType.OAuthSignIn:
      return "Failed to sign in with the selected provider. Please try again."
    case AuthErrorType.OAuthCallback:
      return "Failed to complete OAuth sign in. Please try again."
    case AuthErrorType.OAuthCreateAccount:
      return "Failed to create account with the selected provider."
    case AuthErrorType.OAuthAccountNotLinked:
      return "This account is already linked to another provider."
    case AuthErrorType.SessionRequired:
      return "You must be signed in to access this page."
    default:
      return "An unexpected error occurred. Please try again."
  }
}

/**
 * Format user data for display
 */
export function formatUserData(user: any) {
  return {
    id: user.id,
    email: user.email,
    name: user.name || "User",
    image: user.image || user.avatar_url || null,
    initials: getInitials(user.name || user.email),
  }
}

/**
 * Get initials from name or email
 */
function getInitials(nameOrEmail: string): string {
  if (!nameOrEmail) return "U"
  
  // If it's an email, use the first letter
  if (nameOrEmail.includes("@")) {
    return nameOrEmail[0].toUpperCase()
  }
  
  // If it's a name, get first letters of first and last name
  const parts = nameOrEmail.split(" ")
  if (parts.length >= 2) {
    return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase()
  }
  
  return nameOrEmail[0].toUpperCase()
}

/**
 * Check if error is due to network issues
 */
export function isNetworkError(error: any): boolean {
  return (
    error?.code === "ECONNREFUSED" ||
    error?.code === "ENOTFOUND" ||
    error?.message?.includes("fetch failed") ||
    error?.message?.includes("Network request failed")
  )
}

/**
 * Get redirect URL after authentication
 */
export function getAuthRedirectUrl(callbackUrl?: string | null): string {
  // Default redirect to dashboard
  const defaultRedirect = "/dashboard"
  
  if (!callbackUrl) return defaultRedirect
  
  // Ensure the callback URL is safe (same origin)
  try {
    const url = new URL(callbackUrl, window.location.origin)
    if (url.origin === window.location.origin) {
      return url.pathname + url.search + url.hash
    }
  } catch {
    // Invalid URL
  }
  
  return defaultRedirect
}